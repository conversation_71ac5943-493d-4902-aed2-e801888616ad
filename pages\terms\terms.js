//terms.js
Page({
  data: {
    currentTab: 0, // 当前选中的标签页

    // 默认图片URL（使用网络图片）
    defaultImageUrl: 'https://picsum.photos/120/80?random=999',

    // 片区分析数据
    areaAnalysis: [
      {
        id: 1,
        title: '香蜜湖片区分析',
        subtitle: '',
        date: '荣杏   2024/11/24',
        liop:'2300+',
        imageUrl: 'https://picsum.photos/120/80?random=1',
        showImage: true
      },
      {
        id: 2,
        title: '华侨城片区分析',
        subtitle: '',
        date: '荣杏 2020/12/24',
        imageUrl: 'https://picsum.photos/120/80?random=2',
        showImage: true
      },
      {
        id: 3,
        title: '深圳湾片区分析',
        subtitle: '',
        date: '荣杏 2020/6/24',
        imageUrl: 'https://picsum.photos/120/80?random=3',
        showImage: true
      },
      {
        id: 4,
        title: '后海片区分析',
        subtitle: '',
        date: '荣杏 6/25/24',
        imageUrl: 'https://picsum.photos/120/80?random=4',
        showImage: true
      },
      {
        id: 5,
        title: '前海片区分析',
        subtitle: '',
        date: '荣杏 6/10/24',
        imageUrl: 'https://picsum.photos/120/80?random=5',
        showImage: true
      },
      {
        id: 6,
        title: '南山科技园片区分析',
        subtitle: '',
        date: '荣杏 6/18/24',
        imageUrl: 'https://picsum.photos/120/80?random=6',
        showImage: true
      },
      {
        id: 7,
        title: '蛇口片区分析',
        subtitle: '',
        date: '荣杏 5/22/24',
        imageUrl: 'https://picsum.photos/120/80?random=7',
        showImage: true
      },
      {
        id: 8,
        title: '宝中片区分析',
        subtitle: '',
        date: '荣杏 8/14/24',
        imageUrl: 'https://picsum.photos/120/80?random=8',
        showImage: true
      }
    ],

    // 楼盘评测数据
    buildingEvaluation: [
      {
        id: 1,
        title: '万科云城评测',
        subtitle: '',
        date: '荣杏 7/15/24',
        imageUrl: 'https://picsum.photos/120/80?random=11',
        showImage: true
      },
      {
        id: 2,
        title: '招商蛇口太子湾',
        subtitle: '',
        date: '荣杏 7/20/24',
        imageUrl: 'https://picsum.photos/120/80?random=12',
        showImage: true
      },
      {
        id: 3,
        title: '华润城润府',
        subtitle: '',
        date: '荣杏 7/25/24',
        imageUrl: 'https://picsum.photos/120/80?random=13',
        showImage: true
      }
    ],

    // 学位分析数据
    schoolAnalysis: [
      {
        id: 1,
        title: '南山实验学校学位房',
        subtitle: '',
        date: '荣杏 2024/12/24',
        imageUrl: 'https://picsum.photos/120/80?random=21',
        showImage: true
      },
      {
        id: 2,
        title: '深圳中学学位房',
        subtitle: '',
        date: '荣杏 7/18/24',
        imageUrl: 'https://picsum.photos/120/80?random=22',
        showImage: true
      },
      {
        id: 3,
        title: '荔园小学学位房',
        subtitle: '',
        date: '荣杏 7/22/24',
        imageUrl: 'https://picsum.photos/120/80?random=23',
        showImage: true
      }
    ]
  },

  onLoad() {
    console.log('买房攻略页面加载完成');
    console.log('当前数据:', this.data);
    // 页面加载时获取图片数据
    this.loadImagesFromAPI();
  },

  // 切换标签页
  switchTab(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    this.setData({
      currentTab: index
    });

    console.log('切换到标签页:', index);

    // 预留：可以在这里添加数据加载逻辑
    this.loadTabData(index);
  },

  // 加载标签页数据（预留API接口调用位置）
  loadTabData(tabIndex) {
    const tabNames = ['片区分析', '楼盘评测', '学位分析'];
    console.log(`加载${tabNames[tabIndex]}数据`);

    // TODO: 预留API接口调用位置
    // 后续可以在这里调用微信公众号API获取数据
    /*
    switch(tabIndex) {
      case 0:
        this.loadAreaAnalysisData();
        break;
      case 1:
        this.loadBuildingEvaluationData();
        break;
      case 2:
        this.loadSchoolAnalysisData();
        break;
    }
    */
  },

  // 内容项点击事件
  onItemTap(e) {
    const { type, item } = e.currentTarget.dataset;

    console.log('点击内容项:', type, item);

    // 显示点击反馈
    wx.showToast({
      title: `查看${item.title}`,
      icon: 'none',
      duration: 1500
    });

    // 预留：API接口调用位置
    this.callWeChatAPI(type, item);
  },

  // 调用微信公众号API（预留接口）
  callWeChatAPI(type, item) {
    console.log('准备调用微信公众号API:', type, item);

    // TODO: 预留微信公众号API接口调用
    // 这里可以根据不同类型调用不同的API接口
    /*
    const apiConfig = {
      area: '/api/area-analysis',
      building: '/api/building-evaluation',
      school: '/api/school-analysis'
    };

    const apiUrl = apiConfig[type];
    if (apiUrl) {
      wx.request({
        url: `https://your-wechat-api.com${apiUrl}`,
        method: 'POST',
        data: {
          id: item.id,
          title: item.title,
          type: type
        },
        success: (res) => {
          console.log('API调用成功:', res.data);
          // 处理API返回数据
        },
        fail: (err) => {
          console.error('API调用失败:', err);
        }
      });
    }
    */
  },

  // 预留：片区分析数据加载
  loadAreaAnalysisData() {
    // TODO: 调用片区分析API
    console.log('加载片区分析数据');
  },

  // 预留：楼盘评测数据加载
  loadBuildingEvaluationData() {
    // TODO: 调用楼盘评测API
    console.log('加载楼盘评测数据');
  },

  // 预留：学位分析数据加载
  loadSchoolAnalysisData() {
    // TODO: 调用学位分析API
    console.log('加载学位分析数据');
  },

  // 从API加载图片数据
  loadImagesFromAPI() {
    console.log('开始加载图片数据');

    // 模拟API调用获取图片数据
    setTimeout(() => {
      this.updateImagesData();
    }, 1000);

    // TODO: 实际API调用示例
    /*
    wx.request({
      url: 'https://your-api.com/api/images',
      method: 'GET',
      success: (res) => {
        console.log('图片API调用成功:', res.data);
        this.updateImagesWithAPIData(res.data);
      },
      fail: (err) => {
        console.error('图片API调用失败:', err);
        // 使用默认图片
        this.updateImagesData();
      }
    });
    */
  },

  // 更新图片数据（模拟数据）
  updateImagesData() {
    const updatedAreaAnalysis = this.data.areaAnalysis.map(item => ({
      ...item,
      imageUrl: item.imageUrl || this.data.defaultImageUrl,
      showImage: true
    }));

    const updatedBuildingEvaluation = this.data.buildingEvaluation.map(item => ({
      ...item,
      imageUrl: item.imageUrl || this.data.defaultImageUrl,
      showImage: true
    }));

    const updatedSchoolAnalysis = this.data.schoolAnalysis.map(item => ({
      ...item,
      imageUrl: item.imageUrl || this.data.defaultImageUrl,
      showImage: true
    }));

    this.setData({
      areaAnalysis: updatedAreaAnalysis,
      buildingEvaluation: updatedBuildingEvaluation,
      schoolAnalysis: updatedSchoolAnalysis
    });

    console.log('图片数据更新完成');
  },

  // 图片加载成功事件
  onImageLoad(e) {
    const itemId = e.currentTarget.dataset.itemId;
    console.log(`图片加载成功 - Item ID: ${itemId}`);
  },

  // 图片加载失败事件
  onImageError(e) {
    const itemId = e.currentTarget.dataset.itemId;
    console.log(`图片加载失败 - Item ID: ${itemId}`);

    // 图片加载失败时使用默认图片
    this.updateImageForItem(itemId, this.data.defaultImageUrl);
  },

  // 更新特定项目的图片
  updateImageForItem(itemId, newImageUrl) {
    const updateArrayImage = (array, id, url) => {
      return array.map(item => {
        if (item.id === parseInt(id)) {
          return { ...item, imageUrl: url };
        }
        return item;
      });
    };

    this.setData({
      areaAnalysis: updateArrayImage(this.data.areaAnalysis, itemId, newImageUrl),
      buildingEvaluation: updateArrayImage(this.data.buildingEvaluation, itemId, newImageUrl),
      schoolAnalysis: updateArrayImage(this.data.schoolAnalysis, itemId, newImageUrl)
    });
  },

  // 动态控制图片显示/隐藏
  toggleImageVisibility(itemType, itemId, visible) {
    const updateArrayVisibility = (array, id, show) => {
      return array.map(item => {
        if (item.id === parseInt(id)) {
          return { ...item, showImage: show };
        }
        return item;
      });
    };

    const dataKey = itemType === 'area' ? 'areaAnalysis' :
                   itemType === 'building' ? 'buildingEvaluation' : 'schoolAnalysis';

    this.setData({
      [dataKey]: updateArrayVisibility(this.data[dataKey], itemId, visible)
    });

    console.log(`${itemType} 项目 ${itemId} 图片${visible ? '显示' : '隐藏'}`);
  },

  // 批量更新图片（从API获取的数据）
  updateImagesWithAPIData(apiData) {
    console.log('使用API数据更新图片:', apiData);

    // 假设API返回的数据格式为：
    // { areaImages: [...], buildingImages: [...], schoolImages: [...] }

    if (apiData.areaImages) {
      const updatedAreaAnalysis = this.data.areaAnalysis.map(item => {
        const apiImage = apiData.areaImages.find(img => img.id === item.id);
        return apiImage ? { ...item, imageUrl: apiImage.url } : item;
      });

      this.setData({ areaAnalysis: updatedAreaAnalysis });
    }

    if (apiData.buildingImages) {
      const updatedBuildingEvaluation = this.data.buildingEvaluation.map(item => {
        const apiImage = apiData.buildingImages.find(img => img.id === item.id);
        return apiImage ? { ...item, imageUrl: apiImage.url } : item;
      });

      this.setData({ buildingEvaluation: updatedBuildingEvaluation });
    }

    if (apiData.schoolImages) {
      const updatedSchoolAnalysis = this.data.schoolAnalysis.map(item => {
        const apiImage = apiData.schoolImages.find(img => img.id === item.id);
        return apiImage ? { ...item, imageUrl: apiImage.url } : item;
      });

      this.setData({ schoolAnalysis: updatedSchoolAnalysis });
    }
  }
})
