/* pages/planet-member/planet-member.wxss */

.container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 120rpx;
}
.lop{
  width: 750rpx;
  margin-top:-210rpx ;
 }
 .price-section {
  margin: 0 30rpx 30rpx;
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  width: 750rpx;
  position: relative;
}

.price-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.service-type {
  font-size: 28rpx;
  color: #666;
  position: absolute;
  top: 20rpx;
  left: 55rpx;
}

.price-container {
  display: flex;
  align-items: center;
  position: relative;
  top: 30rpx;
  left: 30rpx;
}

.price-symbol {
  font-size: 32rpx;
  color: #FF4444;
  font-weight: bold;
}

.price-amount {
  font-size: 48rpx;
  color: #FF4444;
  font-weight: bold;
}
.buy-count {
  font-size: 24rpx;
  color: #999;
  position: absolute;
  top: 80rpx;
  left: 630rpx;
}

/* 通用样式 */
.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

/* 头部横幅 */
.header-banner {
  position: relative;
  width: 100%;
  height: 400rpx;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.6));
  display: flex;
  align-items: center;
  justify-content: center;
}

.banner-content {
  text-align: center;
  color: white;
}

.banner-title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 15rpx;
}

.banner-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 知识星球介绍样式 */
.planet-intro-section {
  padding: 40rpx 30rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.intro-subtitle {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 40rpx;
}

.planet-features {
  margin-bottom: 40rpx;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  background: white;
  border-radius: 15rpx;
  padding: 25rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.feature-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.feature-content {
  flex: 1;
}

.feature-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.planet-highlights {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.highlights-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 25rpx;
}

.highlights-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15rpx;
}

.highlight-item {
  display: flex;
  align-items: center;
  padding: 12rpx;
}

.highlight-dot {
  color: #FFD700;
  font-size: 24rpx;
  margin-right: 10rpx;
}

.highlight-text {
  font-size: 24rpx;
  color: #333;
  line-height: 1.4;
}

/* 购买流程样式 */
.purchase-steps-section {
  padding: 40rpx 30rpx;
  background: white;
  width: 670rpx;
}

.steps-container {
  position: relative;
}

.step-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 50rpx;
  position: relative;
}

.step-item:last-child {
  margin-bottom: 0;
}

.step-icon-wrapper {
  position: relative;
  margin-right: 25rpx;
  flex-shrink: 0;
}

.step-icon {
  font-size: 48rpx;
  display: block;
  text-align: center;
  margin-bottom: 8rpx;
}

.step-number {
  width: 40rpx;
  height: 40rpx;
  background: linear-gradient(135deg, #4169E1 0%, #1E90FF 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin: 0 auto;
}

.step-content {
  flex: 1;
  padding-top: 10rpx;
}

.step-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.step-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.step-line {
  position: absolute;
  left: 44rpx;
  top: 90rpx;
  width: 2rpx;
  height: 50rpx;
  background: #e0e0e0;
}

/* 底部购买按钮样式 */
.bottom-purchase-section {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 750rpx;
  padding: 20rpx 30rpx;
  z-index: 100;
}

.purchase-btn {
  width: 100%;
  height: 88rpx;
  background: #ccc;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.purchase-btn.active {
  background: linear-gradient(135deg, #FF4444 0%, #FF6B6B 100%);
  box-shadow: 0 4rpx 16rpx rgba(255, 68, 68, 0.3);
}

/* 会员等级选择 */
.membership-section {
  padding: 40rpx 30rpx;
}

.membership-cards {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.membership-card {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  border: 3rpx solid transparent;
  position: relative;
  transition: all 0.3s ease;
}

.membership-card.selected {
  border-color: #4169E1;
  transform: scale(1.02);
}

.popular-tag {
  position: absolute;
  top: 0;
  right: 0;
  background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
  color: white;
  padding: 8rpx 20rpx;
  border-radius: 0 20rpx 0 20rpx;
  z-index: 2;
}

.popular-text {
  font-size: 22rpx;
  font-weight: bold;
}

.card-header {
  padding: 30rpx;
  text-align: center;
}

.level-name {
  font-size: 32rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 15rpx;
}

.price-section {
  margin-bottom: 10rpx;
}

.current-price {
  font-size: 48rpx;
  font-weight: bold;
  color: #FF4444;
  margin-right: 15rpx;
}

.original-price {
  font-size: 28rpx;
  color: #999;
  text-decoration: line-through;
}

.duration {
  font-size: 24rpx;
  color: #666;
}

.card-body {
  padding: 0 30rpx 30rpx;
}

.privileges-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.privilege-item {
  display: flex;
  align-items: center;
}

.privilege-dot {
  color: #4CAF50;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 12rpx;
}

.privilege-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
}

/* 会员特权介绍 */
.benefits-section {
  padding: 40rpx 30rpx;
  background: #f8f9fa;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 25rpx;
}

.benefit-item {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.benefit-icon {
  font-size: 60rpx;
  display: block;
  margin-bottom: 15rpx;
}

.benefit-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.benefit-desc {
  font-size: 22rpx;
  color: #666;
  line-height: 1.5;
}

/* 表单样式 */
.form-section {
  padding: 40rpx 30rpx;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.form-input,
.form-textarea {
  width: 100%;
  background: #f8f8f8;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.form-input {
  height: 80rpx;
}

.form-textarea {
  min-height: 120rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: #ccc;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40rpx;
}

.submit-btn.active {
  background: linear-gradient(135deg, #4169E1 0%, #1E90FF 100%);
  box-shadow: 0 4rpx 16rpx rgba(65, 105, 225, 0.3);
}

/* FAQ样式 */
.faq-section {
  padding: 40rpx 30rpx;
  background: #f8f9fa;
}

.faq-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.faq-item {
  background: white;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.faq-question {
  padding: 25rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}

.question-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.toggle-icon {
  font-size: 24rpx;
  color: #666;
  transition: transform 0.3s ease;
}

.toggle-icon.expanded {
  transform: rotate(180deg);
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.faq-answer.show {
  max-height: 200rpx;
}

.answer-text {
  display: block;
  padding: 25rpx;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  border-top: 1rpx solid #f0f0f0;
}

/* 半屏弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.member-modal {
  width: 100%;
  background: white;
  border-radius: 30rpx 30rpx 0 0;
  position: relative;
  animation: slideUp 0.3s ease-out;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.15);
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.modal-handle {
  width: 80rpx;
  height: 8rpx;
  background: #e0e0e0;
  border-radius: 4rpx;
  margin: 20rpx auto 10rpx;
}

.modal-close {
  position: absolute;
  top: 20rpx;
  right: 30rpx;
  width: 50rpx;
  height: 50rpx;
  background: rgba(0, 0, 0, 0.1);
  color: #999;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
}

.member-content {
  padding: 20rpx 30rpx 40rpx;
}

/* 成功状态头部 */
.success-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.success-icon {
  font-size: 80rpx;
  margin-bottom: 15rpx;
}

.success-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #4CAF50;
  margin-bottom: 10rpx;
}

.success-subtitle {
  font-size: 26rpx;
  color: #666;
}

/* 会员编号区域 */
.membership-id-section {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 15rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.id-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
}

.id-value {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
  font-family: monospace;
}

.copy-btn {
  background: #f3260b;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

/* 二维码区域 */
.qrcode-section {
  text-align: center;
  margin-bottom: 30rpx;
}

.qrcode-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.qrcode-container {
  position: relative;
  width: 300rpx;
  height: 300rpx;
  margin: 0 auto;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.qrcode-image {
  width: 100%;
  height: 100%;
}

.qrcode-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qrcode-text {
  color: white;
  font-size: 24rpx;
}

/* 会员权益提醒 */
.member-privileges {
  background: linear-gradient(135deg, #E8F5E8 0%, #F0F8FF 100%);
  border-radius: 15rpx;
  padding: 25rpx;
  margin-bottom: 30rpx;
}

.privileges-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.member-privileges .privilege-item {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.member-privileges .privilege-item:last-child {
  margin-bottom: 0;
}

.privilege-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.member-privileges .privilege-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 20rpx;
}

.save-qr-btn,
.close-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: bold;
}

.save-qr-btn {
  background: linear-gradient(135deg, #FF4444 0%, #FF6B6B 100%);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(65, 105, 225, 0.3);
}

.close-btn {
  background: #f5f5f5;
  color: #666;
  border: 1rpx solid #e0e0e0;
}

/* 支付弹窗样式 */
.payment-modal {
  width: 100%;
  background: white;
  border-radius: 30rpx 30rpx 0 0;
  position: relative;
  animation: slideUp 0.3s ease-out;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.15);
}

.payment-content {
  padding: 20rpx 30rpx 40rpx;
}

/* 支付头部 */
.payment-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.payment-icon {
  font-size: 80rpx;
  margin-bottom: 15rpx;
}

.payment-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.payment-subtitle {
  font-size: 26rpx;
  color: #666;
}

/* 订单信息 */
.order-info {
  background: #f8f9fa;
  border-radius: 15rpx;
  padding: 25rpx;
  margin-bottom: 30rpx;
}

.order-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15rpx;
}

.order-item:last-child {
  margin-bottom: 0;
  padding-top: 15rpx;
  border-top: 1rpx solid #e0e0e0;
}

.order-label {
  font-size: 26rpx;
  color: #666;
}

.order-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.order-value.price {
  font-size: 32rpx;
  color: #FF4444;
  font-weight: bold;
}

/* 订单编号区域 */
.order-id-section {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 15rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

/* 支付二维码区域 */
.payment-qrcode-section {
  text-align: center;
  margin-bottom: 30rpx;
}

/* 支付按钮组 */
.payment-buttons {
  display: flex;
  gap: 20rpx;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: bold;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
  border: 1rpx solid #e0e0e0;
}

.confirm-btn {
  background: linear-gradient(135deg, #FF4444 0%, #FF6B6B 100%);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(255, 68, 68, 0.3);
}
