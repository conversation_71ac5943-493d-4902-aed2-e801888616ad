<!--terms.wxml-->
<view class="container">
  <!-- 标签页切换 -->
  <view class="tab-container">
    <view
      class="tab-item {{currentTab === 0 ? 'active' : ''}}"
      bindtap="switchTab"
      data-index="0"
    >
      <text class="tab-text">片区分析</text>
    </view>
    <view
      class="tab-item {{currentTab === 1 ? 'active' : ''}}"
      bindtap="switchTab"
      data-index="1"
    >
      <text class="tab-text">楼盘评测</text>
    </view>
    <view
      class="tab-item {{currentTab === 2 ? 'active' : ''}}"
      bindtap="switchTab"
      data-index="2"
    >
      <text class="tab-text">学位分析</text>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="content-container">
    <!-- 片区分析内容 -->
    <view class="content-panel {{currentTab === 0 ? 'show' : 'hide'}}">
      <view
        class="content-item"
        wx:for="{{areaAnalysis}}"
        wx:key="id"
        bindtap="onItemTap"
        data-type="area"
        data-item="{{item}}">
        <view class="item-content">
          <text class="item-title">{{item.title}} <text class="item-subtitle">{{item.subtitle}}</text></text>
          <view class="item-date">👁 {{item.liop}}   {{item.date}}</view>
        </view>
        <view class="image-container">
          <image
            class="item-image"
            src="{{item.imageUrl || defaultImageUrl}}"
            mode="aspectFit"
            wx:if="{{item.showImage !== false}}"
            bindload="onImageLoad"
            binderror="onImageError"
            data-item-id="{{item.id}}"
          />
          <view wx:if="{{!item.imageUrl}}" class="image-placeholder">图片</view>
        </view>
      </view>
    </view>

    <!-- 楼盘评测内容 -->
    <view class="content-panel {{currentTab === 1 ? 'show' : 'hide'}}">
    <view class="poo"> 
      <view
        class="content-item"
        wx:for="{{buildingEvaluation}}"
        wx:key="id"
        bindtap="onItemTap"
        data-type="building"
        data-item="{{item}}"
      >
        <view class="item-content">
          <text class="item-title">{{item.title}} <text class="item-subtitle">{{item.subtitle}}</text></text>
          <text class="item-date">{{item.date}}</text>
        </view>
        <view class="image-container">
          <image
            class="item-image"
            src="{{item.imageUrl || defaultImageUrl}}"
            mode="aspectFit"
            wx:if="{{item.showImage !== false}}"
            bindload="onImageLoad"
            binderror="onImageError"
            data-item-id="{{item.id}}"
          />
          <view wx:if="{{!item.imageUrl}}" class="image-placeholder">图片</view>
        </view>
      </view>
      </view>
     
    </view>

    <!-- 学位分析内容 -->
    <view class="content-panel {{currentTab === 2 ? 'show' : 'hide'}}">
     <view class="poi">
      <view
        class="content-item"
        wx:for="{{schoolAnalysis}}"
        wx:key="id"
        bindtap="onItemTap"
        data-type="school"
        data-item="{{item}}"
      >
        <view class="item-content">
          <text class="item-title">{{item.title}}   <text class="item-subtitle">{{item.subtitle}}</text></text>
          <text class="item-date">{{item.date}}</text>
        </view>
        <view class="image-container">
          <image
            class="item-image"
            src="{{item.imageUrl || defaultImageUrl}}"
            mode="aspectFit"
            wx:if="{{item.showImage !== false}}"
            bindload="onImageLoad"
            binderror="onImageError"
            data-item-id="{{item.id}}"
          />
          <view wx:if="{{!item.imageUrl}}" class="image-placeholder">图片</view>
        </view>
      </view>
     </view>
    </view>
  </view>
</view>
