/* pages/property-evaluation/success.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 0 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* 成功图标 */
.success-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 40rpx;
  animation: bounceIn 0.8s ease-out;
}

.success-icon image {
  width: 100%;
  height: 100%;
}

/* 成功标题 */
.success-title {
  margin-bottom: 20rpx;
}

.title-text {
  font-size: 48rpx;
  font-weight: bold;
  color: #f13c0e;
  text-align: center;
}

/* 成功描述 */
.success-description {
  margin-bottom: 50rpx;
  text-align: center;
}

.desc-text {
  font-size: 32rpx;
  color: #666;
  line-height: 1.6;
  max-width: 600rpx;
}

/* 微信信息 */
.wechat-info {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  width: 100%;
  max-width: 600rpx;
  box-sizing: border-box;
}

.wechat-qr {
  width: 120rpx;
  height: 120rpx;
  margin-right: 30rpx;
  flex-shrink: 0;
}

.qr-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.wechat-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.wechat-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.wechat-id {
  font-size: 28rpx;
  color: #4CAF50;
  font-weight: 500;
}

.wechat-note {
  font-size: 24rpx;
  color: #999;
}

/* 温馨提示 */
.tips-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 50rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 600rpx;
  box-sizing: border-box;
}

.tips-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.tip-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  padding-left: 20rpx;
  position: relative;
}

.tip-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 6rpx;
  background-color: #4CAF50;
  border-radius: 50%;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 20rpx;
  width: 100%;
  max-width: 600rpx;
}

.btn {
  flex: 1;
  padding: 30rpx 0;
  border-radius: 50rpx;
  font-size: 30rpx;
  font-weight: bold;
  border: none;
  transition: all 0.3s ease;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #666;
  border: 1rpx solid #e0e0e0;
}

.btn-secondary:active {
  background-color: #e0e0e0;
}

.btn-primary {
  background: linear-gradient(135deg, #FF4444 0%, #FF6B6B 100%);
  color: white;
  box-shadow: 0 8rpx 20rpx rgba(76, 175, 80, 0.3);
}

.btn-primary:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.4);
}

/* 响应式设计 */
@media (max-width: 600rpx) {
  .container {
    padding: 0 30rpx;
  }
  
  .success-icon {
    width: 100rpx;
    height: 100rpx;
    margin-bottom: 30rpx;
  }
  
  .title-text {
    font-size: 42rpx;
  }
  
  .desc-text {
    font-size: 28rpx;
  }
  
  .wechat-info {
    padding: 30rpx;
    flex-direction: column;
    text-align: center;
  }
  
  .wechat-qr {
    margin-right: 0;
    margin-bottom: 20rpx;
    align-self: center;
  }
  
  .tips-section {
    padding: 25rpx;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .btn {
    padding: 25rpx 0;
    font-size: 28rpx;
  }
}

/* 动画效果 */
@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.success-title {
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

.success-description {
  animation: fadeInUp 0.6s ease-out 0.4s both;
}

.wechat-info {
  animation: fadeInUp 0.6s ease-out 0.6s both;
}

.tips-section {
  animation: fadeInUp 0.6s ease-out 0.8s both;
}

.action-buttons {
  animation: fadeInUp 0.6s ease-out 1s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
