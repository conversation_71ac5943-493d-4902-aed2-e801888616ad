// pages/property-evaluation/success.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    evaluationType: '', // 评测类型：buy 或 sell
    wechatId: 'property_expert'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('成功页面加载', options);
    
    // 获取评测类型
    if (options.type) {
      this.setData({
        evaluationType: options.type
      });
    }
  },

  /**
   * 返回首页
   */
  goBack: function() {
    wx.switchTab({
      url: '/pages/index/index',
      fail: () => {
        // 如果不是tabBar页面，使用navigateTo
        wx.navigateTo({
          url: '/pages/property-evaluation/property-evaluation'
        });
      }
    });
  },

  /**
   * 复制微信号
   */
  copyWechat: function() {
    wx.setClipboardData({
      data: this.data.wechatId,
      success: () => {
        wx.showToast({
          title: '微信号已复制',
          icon: 'success',
          duration: 2000
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    const title = this.data.evaluationType === 'buy' ? 
      '房子应不应该买 - 买房前专业评测' : 
      '房子应不应该卖 - 卖房时机专业评测';
      
    return {
      title: title,
      desc: '专业评估，精准分析',
      path: '/pages/property-evaluation/property-evaluation'
    };
  }
});
