/* pages/property-evaluation/property-evaluation.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx 40rpx;
}

/* 页面标题 */
.page-header {
  text-align: center;
  padding: 60rpx 0 40rpx;
  background: linear-gradient(135deg, #FF4444 0%, #FF6B6B 100%);
  color: white;
  border-radius: 0 0 30rpx 30rpx;
  margin: 0 -40rpx 40rpx;
}

.page-title {
  font-size: 44rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.page-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
}

/* 选择按钮区域 */
.selection-container {
  width: 750rpx;
  width: 98%;
}

.option-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  position: relative;
  transition: all 0.3s ease;
}

.option-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.card-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 30rpx;
  flex-shrink: 0;
}

.card-icon image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.card-content {
  flex: 1;
  padding-right: 20rpx;
}

.card-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.card-subtitle {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 15rpx;
}

.card-description {
  font-size: 26rpx;
  color: #999;
  line-height: 1.6;
  white-space: pre-line;
}

.card-arrow {
  font-size: 32rpx;
  color: #FF4444;
  font-weight: bold;
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
}

/* 服务说明 */
.service-info {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.info-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.info-item {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  padding-left: 20rpx;
  position: relative;
}

.info-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 8rpx;
  background-color: #FF4444;
  border-radius: 50%;
}

/* 响应式设计 */
@media (max-width: 600rpx) {
  .container {
    padding: 0 30rpx;
  }

  .page-header {
    margin: 0 -30rpx 30rpx;
    padding: 50rpx 0 30rpx;
  }

  .page-title {
    font-size: 40rpx;
  }

  .option-card {
    padding: 25rpx;
  }

  .card-icon {
    width: 70rpx;
    height: 70rpx;
    margin-right: 25rpx;
  }

  .card-title {
    font-size: 32rpx;
  }

  .card-subtitle {
    font-size: 26rpx;
  }

  .card-description {
    font-size: 24rpx;
  }
}

/* 动画效果 */
.option-card {
  animation: fadeInUp 0.6s ease-out;
}

.option-card:nth-child(1) {
  animation-delay: 0.1s;
}

.option-card:nth-child(2) {
  animation-delay: 0.2s;
}

.service-info {
  animation: fadeInUp 0.6s ease-out 0.3s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}