// pages/property-evaluation/property-evaluation.js
Page({
  /**
   * 页面的初始数据
   */
  data: {

  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('房产价值评测首页加载');

    // 测试页面是否注册
    console.log('检查页面注册状态...');
  },

  /**
   * 测试方法 - 验证点击事件
   */
  testClick: function() {
    console.log('测试点击事件被触发');
    wx.showToast({
      title: '点击事件正常',
      icon: 'success'
    });
  },

  /**
   * 导航到买房评测页面
   */
  navigateToBuyEvaluation: function() {
    console.log('点击了买房评测按钮');

    // 先显示一个提示，确认点击事件被触发
    wx.showToast({
      title: '正在跳转...',
      icon: 'loading',
      duration: 1000
    });

    setTimeout(() => {
      wx.navigateTo({
        url: '/pages/property-evaluation/buy-evaluation',
        success: (res) => {
          console.log('买房评测页面导航成功:', res);
        },
        fail: (err) => {
          console.error('买房评测页面导航失败:', err);
          wx.showToast({
            title: '页面跳转失败: ' + (err.errMsg || '未知错误'),
            icon: 'none',
            duration: 3000
          });
        }
      });
    }, 500);
  },

  /**
   * 导航到卖房评测页面
   */
  navigateToSellEvaluation: function() {
    console.log('点击了卖房评测按钮');

    // 先显示一个提示，确认点击事件被触发
    wx.showToast({
      title: '正在跳转...',
      icon: 'loading',
      duration: 1000
    });

    setTimeout(() => {
      wx.navigateTo({
        url: '/pages/property-evaluation/sell-evaluation',
        success: (res) => {
          console.log('卖房评测页面导航成功:', res);
        },
        fail: (err) => {
          console.error('卖房评测页面导航失败:', err);
          wx.showToast({
            title: '页面跳转失败: ' + (err.errMsg || '未知错误'),
            icon: 'none',
            duration: 3000
          });
        }
      });
    }, 500);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: '房产价值评测',
      desc: '专业评估，精准分析',
      path: '/pages/property-evaluation/property-evaluation'
    };
  }

});