// pages/sell-house/sell-house.js
const api = require('../../config/api.js')
const userService = require('../../services/user.js')
const util = require('../../utils/util.js')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 房屋类型选项
    houseTypes: ['住宅', '公寓', '写字楼', '商业', '工厂', '车位'],
    houseTypeIndex: 0,
    
    // 性别选项
    genderOptions: ['先生', '女士'],
    genderIndex: 0,
    
    // 表单数据
    cityName: '',
    communityName: '',
    houseArea: '',
    expectedPrice: '',
    contactName: '',
    contactPhone: '',
    remarks: '',

    // 协议勾选状态
    agreementChecked: false,

    // 提交状态
    canSubmit: false,
    formComplete: false,
    phoneValid: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('卖房页面加载');
    this.checkLoginStatus();
    // 初始化按钮状态
    this.checkCanSubmit();

    // 调试：输出初始状态
    console.log('页面加载时的初始状态:', {
      agreementChecked: this.data.agreementChecked,
      canSubmit: this.data.canSubmit
    });
  },

  /**
   * 检查登录状态（仅在页面加载时提示）
   */
  checkLoginStatus() {
    const isLoggedIn = userService.checkLoginStatus()
    if (!isLoggedIn) {
      // 延迟显示，避免页面加载时立即弹窗
      setTimeout(() => {
        wx.showModal({
          title: '温馨提示',
          content: '提交房源信息需要先登录，是否现在模拟登录？',
          showCancel: true,
          cancelText: '稍后登录',
          confirmText: '模拟登录',
          success: (res) => {
            if (res.confirm) {
              // 执行模拟登录
              this.performMockLogin();
            }
            // 用户选择稍后登录，可以继续填写表单
          }
        })
      }, 500)
    }
  },

  /**
   * 执行模拟登录
   */
  performMockLogin() {
    util.showLoading('登录中...');

    setTimeout(() => {
      const loginResult = userService.mockLogin();
      util.hideLoading();

      if (loginResult) {
        util.showSuccess('登录成功');
        console.log('模拟登录成功:', loginResult);
      } else {
        util.showError('登录失败');
      }
    }, 1000);
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时检查登录状态
    const isLoggedIn = userService.checkLoginStatus()
    if (!isLoggedIn) {
      // 如果未登录，不显示弹窗，只在提交时提示
      console.log('用户未登录，请在提交时登录')
    }
    this.checkCanSubmit();
  },

  /**
   * 房屋类型选择
   */
  onHouseTypeChange(e) {
    const index = parseInt(e.detail.value);
    this.setData({
      houseTypeIndex: index
    });
    this.checkCanSubmit();
  },

  /**
   * 性别选择
   */
  onGenderChange(e) {
    const index = parseInt(e.detail.value);
    this.setData({
      genderIndex: index
    });
    this.checkCanSubmit();
  },

  /**
   * 城市输入
   */
  onCityInput(e) {
    this.setData({
      cityName: e.detail.value
    });
    this.checkCanSubmit();
  },

  /**
   * 小区名称输入
   */
  onCommunityInput(e) {
    this.setData({
      communityName: e.detail.value
    });
    this.checkCanSubmit();
  },

  /**
   * 面积输入
   */
  onAreaInput(e) {
    const value = this.validateNumberInput(e.detail.value);
    this.setData({
      houseArea: value
    });
    this.checkCanSubmit();
  },

  /**
   * 价格输入
   */
  onPriceInput(e) {
    const value = this.validateNumberInput(e.detail.value);
    this.setData({
      expectedPrice: value
    });
    this.checkCanSubmit();
  },

  /**
   * 联系人输入
   */
  onContactNameInput(e) {
    this.setData({
      contactName: e.detail.value
    });
    this.checkCanSubmit();
  },

  /**
   * 联系电话输入
   */
  onContactPhoneInput(e) {
    const value = this.validatePhoneInput(e.detail.value);
    this.setData({
      contactPhone: value
    });
    this.checkCanSubmit();
  },

  /**
   * 备注输入
   */
  onRemarksInput(e) {
    this.setData({
      remarks: e.detail.value
    });
  },

  /**
   * 协议勾选状态改变
   */
  onAgreementChange(e) {
    const checked = e.detail.value.includes('agreement');
    console.log('协议勾选状态改变:', checked, e.detail.value);
    this.setData({
      agreementChecked: checked
    });
    this.checkCanSubmit();
  },

  /**
   * 调试：切换按钮状态测试
   */
  toggleDebug() {
    const newState = !this.data.canSubmit;
    console.log('手动切换按钮状态:', newState);
    this.setData({
      canSubmit: newState,
      agreementChecked: newState,
      formComplete: newState,
      phoneValid: newState
    });
  },

  /**
   * 验证数字输入
   */
  validateNumberInput(value) {
    // 只允许数字和小数点
    let cleanValue = value.replace(/[^\d.]/g, '');
    
    // 确保只有一个小数点
    const parts = cleanValue.split('.');
    if (parts.length > 2) {
      cleanValue = parts[0] + '.' + parts.slice(1).join('');
    }
    
    // 限制小数点后最多2位
    if (parts.length === 2 && parts[1].length > 2) {
      cleanValue = parts[0] + '.' + parts[1].substring(0, 2);
    }
    
    return cleanValue;
  },

  /**
   * 验证手机号输入
   */
  validatePhoneInput(value) {
    // 只允许数字
    return value.replace(/[^\d]/g, '');
  },

  /**
   * 检查是否可以提交
   */
  checkCanSubmit() {
    const { communityName, houseArea, expectedPrice, contactName, contactPhone, agreementChecked } = this.data;

    // 检查必填字段是否完整
    const requiredFields = {
      communityName: communityName.trim(),
      houseArea: houseArea.trim(),
      contactName: contactName.trim(),
      contactPhone: contactPhone.trim()
    };

    // 验证所有必填字段都已填写
    const formComplete = Object.values(requiredFields).every(field => field !== '') &&
                        contactPhone.length >= 11;

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    const phoneValid = phoneRegex.test(contactPhone);

    // 验证小区名称长度（至少2个字符）
    const communityNameValid = communityName.trim().length >= 2;

    // 验证联系人姓名（至少2个字符，只允许中文和英文）
    const nameRegex = /^[\u4e00-\u9fa5a-zA-Z\s]{2,}$/;
    const contactNameValid = nameRegex.test(contactName.trim());

    // 验证房号格式（不能为空且长度合理）
    const houseAreaValid = houseArea.trim().length >= 1 && houseArea.trim().length <= 20;

    // 最终提交条件：协议已勾选 + 表单完整 + 各字段格式有效
    const canSubmit = agreementChecked && formComplete && phoneValid &&
                     communityNameValid && contactNameValid && houseAreaValid;

    console.log('checkCanSubmit:', {
      agreementChecked,
      formComplete,
      phoneValid,
      communityNameValid,
      contactNameValid,
      houseAreaValid,
      canSubmit,
      requiredFields
    });

    this.setData({
      canSubmit: canSubmit,
      formComplete: formComplete,
      phoneValid: phoneValid
    });
  },

  /**
   * 查看提交记录
   */
  viewSubmitRecord() {
    // 检查登录状态
    const isLoggedIn = userService.checkLoginStatus()
    if (!isLoggedIn) {
      util.showError('请先登录后查看提交记录')
      return
    }

    // 跳转到房源出售记录页面
    wx.navigateTo({
      url: '/pages/house-sale-records/house-sale-records'
    })
  },

  /**
   * 提交表单
   */
  onSubmit() {
    const { agreementChecked, communityName, houseArea, contactName, contactPhone } = this.data;

    // 优先检查协议勾选状态
    if (!agreementChecked) {
      wx.showToast({
        title: '请先同意用户协议',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 详细验证各个必填字段
    if (!communityName.trim()) {
      wx.showToast({
        title: '请输入小区名称',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    if (communityName.trim().length < 2) {
      wx.showToast({
        title: '小区名称至少需要2个字符',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    if (!houseArea.trim()) {
      wx.showToast({
        title: '请输入楼栋及房号',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    if (!contactName.trim()) {
      wx.showToast({
        title: '请输入联系人姓名',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 验证联系人姓名格式
    const nameRegex = /^[\u4e00-\u9fa5a-zA-Z\s]{2,}$/;
    if (!nameRegex.test(contactName.trim())) {
      wx.showToast({
        title: '联系人姓名格式不正确',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    if (!contactPhone.trim()) {
      wx.showToast({
        title: '请输入联系方式',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 检查手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(contactPhone)) {
      wx.showToast({
        title: '请输入正确的手机号码',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 获取当前用户信息
    const currentUser = userService.getLocalUserInfo()
    if (!currentUser || !currentUser.id) {
      // 如果没有用户信息，提供模拟登录选项
      wx.showModal({
        title: '需要登录',
        content: '提交房源信息需要先登录，是否现在模拟登录？',
        confirmText: '模拟登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.performMockLogin();
            // 登录后重新提交
            setTimeout(() => {
              this.onSubmit();
            }, 1500);
          }
        }
      });
      return
    }

    // 构建提交数据（按照API接口要求）
    const submitData = {
      userId: currentUser.id,
      houseType: this.data.houseTypes[this.data.houseTypeIndex],
      city: this.data.cityName,
      communityName: this.data.communityName,
      housingArea: parseFloat(this.data.houseArea),
      expectedPrice: parseFloat(this.data.expectedPrice),
      contactPerson: this.data.contactName,
      gender: this.data.genderOptions[this.data.genderIndex],
      contactInfo: this.data.contactPhone,
      remarks: this.data.remarks || ''
    };

    console.log('提交数据:', submitData);

    // 调用房源出售登记API
    this.submitHouseSale(submitData);
  },

  /**
   * 提交房源出售登记
   */
  async submitHouseSale(submitData) {
    try {
      util.showLoading('提交中...')

      // 获取用户token
      const accessToken = wx.getStorageSync('accessToken')
      if (!accessToken) {
        util.hideLoading()
        util.showError('请先登录')
        return
      }

      // 调用房源出售登记API
      const response = await api.post(api.API.HOUSE_SALE_CREATE, submitData, {
        'Authorization': `Bearer ${accessToken}`
      })

      util.hideLoading()

      if (response.code === 200) {
        // 提交成功
        console.log('房源登记成功:', response.data)

        wx.showModal({
          title: '提交成功',
          content: `您的房源信息已提交成功，登记ID：${response.data.id}，我们会尽快联系您！`,
          showCancel: false,
          confirmText: '确定',
          success: (res) => {
            if (res.confirm) {
              // 清空表单
              this.resetForm()
              // 返回上一页
              wx.navigateBack()
            }
          }
        })
      } else {
        // 提交失败
        util.showError(response.message || '提交失败，请重试')
      }

    } catch (error) {
      util.hideLoading()
      console.error('提交房源信息失败:', error)

      const errorMessage = error.message || error.errMsg || '提交失败'
      if (errorMessage.includes('登录') || errorMessage.includes('401')) {
        util.showError('登录已过期，请重新登录')
      } else if (errorMessage.includes('网络')) {
        util.showError('网络连接失败，请检查网络')
      } else {
        util.showError('提交失败，请重试')
      }
    }
  },

  /**
   * 重置表单
   */
  resetForm() {
    this.setData({
      houseTypeIndex: 0,
      genderIndex: 0,
      cityName: '',
      communityName: '',
      houseArea: '',
      expectedPrice: '',
      contactName: '',
      contactPhone: '',
      remarks: '',
      canSubmit: false
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '锦绣资产 - 我要卖房',
      path: '/pages/sell-house/sell-house'
    };
  }
});
