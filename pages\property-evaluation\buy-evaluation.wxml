<!--pages/property-evaluation/buy-evaluation.wxml-->
<view class="container">
  <!-- 表单区域 -->
  <view class="form-container">
    <!-- 房屋信息表单 -->
    <view class="form-section">
  <!-- 提示文字 -->
  <view class="submit-record">
      <text class="record-text">请根据您的实际情况填写，提供的信息越详细，评估结果越准确。</text>
    </view>
      <!-- 小区名称 -->
      <view class="form-item">
        <view class="form-label">小区名称</view>
        <view class="form-input-container">
          <input class="form-input" placeholder="请输入小区名称" value="{{formData.communityName}}" data-field="communityName" bindinput="onInput"/>
        </view>
      </view>

      <!-- 房产类型 -->
      <picker range="{{propertyTypeOptions}}" value="{{pickerIndexes.propertyType}}" data-field="propertyType" bindchange="onPickerChange" style="border-bottom: 1rpx solid rgb(204, 199, 199);">
        <view class="form-item">
          <view class="form-label">房产类型</view>
          <view class="form-value-container">
            <text class="form-value">{{formData.propertyType || '请选择房产类型'}}</text>
            <text class="arrow-text">></text>
          </view>
        </view>
      </picker>

      <!-- 其他房产类型输入 -->
      <view wx:if="{{formData.propertyType === '其他'}}" class="form-item">
        <view class="form-label">请具体说明</view>
        <view class="form-input-container">
          <input class="form-input" placeholder="请具体说明房产类型" value="{{formData.otherPropertyType}}" data-field="otherPropertyType" bindinput="onInput"/>
        </view>
      </view>

      <!-- 新房or二手房 -->
      <view class="form-item">
        <view class="form-label">新房or二手房</view>
        <view class="radio-group">
          <view
            class="radio-item {{formData.houseAge === '新房' ? 'selected' : ''}}"
            bindtap="onRadioSelect"
            data-field="houseAge"
            data-value="新房"
          >
            <view class="radio-circle">
              <view wx:if="{{formData.houseAge === '新房'}}" class="radio-dot"></view>
            </view>
            <text class="radio-text">新房</text>
          </view>
          <view
            class="radio-item {{formData.houseAge === '二手房' ? 'selected' : ''}}"
            bindtap="onRadioSelect"
            data-field="houseAge"
            data-value="二手房"
          >
            <view class="radio-circle">
              <view wx:if="{{formData.houseAge === '二手房'}}" class="radio-dot"></view>
            </view>
            <text class="radio-text">二手房</text>
          </view>
        </view>
      </view>

      <!-- 建筑面积 -->
      <view class="form-item">
        <view class="form-label">建筑面积m²</view>
        <view class="form-input-container">
          <input class="form-input" placeholder="请输入建筑面积" type="digit" value="{{formData.buildingArea}}" data-field="buildingArea" bindinput="onInput"/>
         
        </view>
      </view>

      <!-- 户型 -->
      <picker range="{{houseTypeOptions}}" value="{{pickerIndexes.houseType}}" data-field="houseType" bindchange="onPickerChange" style="border-bottom: 1rpx solid rgb(204, 199, 199);">
        <view class="form-item">
          <view class="form-label">户型</view>
          <view class="form-value-container">
            <text class="form-value">{{formData.houseType || '请选择户型'}}</text>
            <text class="arrow-text">></text>
          </view>
        </view>
      </picker>

      <!-- 朝向 -->
      <view class="form-item">
        <view class="form-label">朝向</view>
        <view class="form-input-container">
          <input class="form-input" placeholder="请输入朝向" value="{{formData.orientation}}" data-field="orientation" bindinput="onInput"/>
        </view>
      </view>

      <!-- 楼层 -->
      <view class="form-item">
        <view class="form-label">楼层</view>
        <view class="form-input-container">
          <input class="form-input" placeholder="请输入楼层" value="{{formData.floor}}" data-field="floor" bindinput="onInput"/>
        </view>
      </view>

      <!-- 装修情况 -->
      <picker range="{{decorationOptions}}" value="{{pickerIndexes.decoration}}" data-field="decoration" bindchange="onPickerChange" style="border-bottom: 1rpx solid rgb(204, 199, 199);">
        <view class="form-item">
          <view class="form-label">装修情况</view>
          <view class="form-value-container">
            <text class="form-value">{{formData.decoration || '请选择装修情况'}}</text>
            <text class="arrow-text">></text>
          </view>
        </view>
      </picker>

      <!-- 景观/视野 -->
      <view class="form-item">
        <view class="form-label">景观/视野</view>
        <view class="form-input-container">
          <input class="form-input" placeholder="请描述景观视野" value="{{formData.view}}" data-field="view" bindinput="onInput"/>
        </view>
      </view>

      <!-- 目前状态 -->
      <picker range="{{currentStatusOptions}}" value="{{pickerIndexes.currentStatus}}" data-field="currentStatus" bindchange="onPickerChange" style="border-bottom: 1rpx solid rgb(204, 199, 199);">
        <view class="form-item">
          <view class="form-label">目前状态</view>
          <view class="form-value-container">
            <text class="form-value">{{formData.currentStatus || '请选择目前状态'}}</text>
            <text class="arrow-text">></text>
          </view>
        </view>
      </picker>

      <!-- 房子总价 -->
      <view class="form-item">
        <view class="form-label">房子总价(万)</view>
        <view class="form-input-container">
          <input class="form-input" placeholder="请输入房子总价" type="digit" value="{{formData.totalPrice}}" data-field="totalPrice" bindinput="onInput"/>
          <text class="form-unit"></text>
        </view>
      </view>

      <!-- 税费 -->
      <view class="form-item">
        <view class="form-label">税费(万)</view>
        <view class="form-input-container">
          <input class="form-input" placeholder="请输入税费金额" type="digit" value="{{formData.taxes}}" data-field="taxes" bindinput="onInput"/>
          <text class="form-unit"></text>
        </view>
      </view>

      <!-- 中介费 -->
      <view class="form-item" style="border-bottom: 1rpx solid rgb(204, 199, 199);">
        <view class="form-label">中介费(万)</view>
        <view class="form-input-container">
          <input class="form-input" placeholder="请输入中介费金额" type="digit" value="{{formData.agencyFee}}" data-field="agencyFee" bindinput="onInput"/>
          <text class="form-unit"></text>
        </view>
      </view>
    </view>

    <!-- 备注信息 -->
    <view class="form-section">
      <view class="section-title">个人补充说明</view>
      <view class="form-item textarea-item">
        <textarea class="form-textarea" placeholder="请补充其他相关信息（可选）" value="{{formData.additionalInfo}}" data-field="additionalInfo" bindinput="onInput" maxlength="500" style="height: 135rpx; display: block; box-sizing: border-box; left: 0rpx; top: 0rpx"></textarea>
      </view>
    </view>

  
  </view>

  <!-- 提交按钮 -->
  <view class="submit-container">
    <button class="submit-btn" bindtap="submitForm" style="width: 650rpx;">提交评测</button>
  </view>
</view>
