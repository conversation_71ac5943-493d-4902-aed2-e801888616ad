/* pages/sell-house/sell-house.wxss */
.container {
  background-color: #f5f5f5;
  padding:30rpx 20rpx ;
}

/* 表单容器 */
.form-container {
  width: 100%;
  margin-top: -40rpx;
}

/* 表单区块 */
.form-section {
  background-color: white;
  overflow: hidden;
  width: 100%;
}

.section-title {
  background-color: #FFFFFF;
  padding: 40rpx;
  font-size: 32rpx;
  color: #333;
}

/* 表单项 */
.form-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 20rpx;
  min-height: 80rpx;
  border-bottom: 1rpx solid rgb(204, 199, 199);
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  min-width: 160rpx;
}

/* 必填标记样式 */
.required-mark {
  color: #FF4444;
  margin-left: 4rpx;
}

/* 输入框容器 */
.form-input-container {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-end;
}

.form-input {
  flex: 1;
  text-align: right;
  font-size: 30rpx;
  color: #333;
  padding: 0 20rpx;
}

.form-unit {
  font-size: 28rpx;
  color: #666;
  margin-left: 10rpx;
}

/* 选择器值容器 */
.form-value-container {
  display: flex;
  align-items: center;
}

.form-value {
  font-size: 30rpx;
  color: #333;
  margin-right: 10rpx;
}

.arrow-text {
  font-size: 28rpx;
  color: #999;
}

/* 文本域项 */
.textarea-item {
  flex-direction: column;
  align-items: stretch;
  padding: 30rpx;
}

.form-textarea {
  width: 100%;
  min-height: 100rpx;
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
  padding: 20rpx;
  border-radius: 10rpx;
  background-color: #FFFFFF
}

/* 提示信息样式 */
.tip-container {
  display: flex;
  align-items: flex-start;
  padding: 20rpx 30rpx;
  margin-top: 10rpx;
}

.tip-icon {
  width: 24rpx;
  height: 24rpx;
  border: 2rpx solid #999;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16rpx;
  color: #999;
  margin-right: 15rpx;
  margin-top: 2rpx;
  flex-shrink: 0;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
  flex: 1;
}

/* 提交记录链接 */
.submit-record {
  text-align: center;
  padding: 30rpx;
}

.record-link {
  color: #FF4444;
  font-size: 28rpx;
  text-decoration: underline;
}

/* 用户协议勾选样式 */
.agreement-container {
  padding: 20rpx 30rpx;
  background-color: #f5f5f5;
  min-height: 10vh;
}

.agreement-checkbox-container {
  display: flex;
  align-items: flex-start;
  gap: 15rpx;
}

.agreement-checkbox {
  margin-top: 2rpx;
  transform: scale(1.2);
  flex-shrink: 0;
}

/* 勾选状态下的复选框样式 */
checkbox[checked] {
  color: #FF4444 !important;
}

/* 微信小程序复选框勾选状态样式 */
.wx-checkbox-input.wx-checkbox-input-checked {
  background-color: #FF4444 !important;
  border-color: #FF4444 !important;
}

.wx-checkbox-input.wx-checkbox-input-checked::before {
  color: white !important;
}

.agreement-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  flex: 1;
}

/* 调试信息样式 */
.debug-info {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 10rpx;
  border: 1rpx solid #e9ecef;
}

.debug-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.debug-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rpx 0;
  border-bottom: 1rpx solid #e9ecef;
}

.debug-item:last-child {
  border-bottom: none;
}

.debug-label {
  font-size: 24rpx;
  color: #666;
}

.debug-value {
  font-size: 24rpx;
  font-weight: bold;
}

.debug-value.valid {
  color: #28a745;
}

.debug-value.invalid {
  color: #dc3545;
}

.debug-note {
  font-size: 20rpx;
  color: #999;
  margin-left: 10rpx;
}

/* 调试按钮样式 */
.debug-buttons {
  margin-top: 15rpx;
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}

.debug-btn {
  flex: 1;
  min-width: 120rpx;
  font-size: 20rpx !important;
  background-color: #007bff !important;
  color: white !important;
  border-radius: 6rpx !important;
  padding: 8rpx 12rpx !important;
}

/* 提交按钮容器 */
.submit-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
}

.submit-btn {
  width: 750rpx;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 44rpx;
  border: none;
  transition: all 0.3s ease;
}

/* 启用状态样式 */
.submit-btn:not(.disabled):not(:disabled) {
  background: linear-gradient(135deg, #FF4444 0%, #FF6B6B 100%);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(255, 68, 68, 0.3);
}

/* 禁用状态样式 */
.submit-btn:disabled,
.submit-btn.disabled {
  background: #ccc !important;
  background-image: none !important;
  box-shadow: none !important;
  color: #999 !important;
}

.submit-btn:not(:disabled):active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 68, 68, 0.3);
}

/* 响应式设计 */
@media (max-width: 375px) {
  .form-label {
    min-width: 140rpx;
    font-size: 30rpx;
  }
  
  .form-input {
    font-size: 28rpx;
  }
  
  .form-value {
    font-size: 28rpx;
  }
}

/* 输入框聚焦状态 */
.form-input:focus {
  color: #FF4444;
}

/* 选择器项目悬停效果 */
.form-item:active {
  background-color: #f8f9fa;
}

/* 加载状态 */
.loading {
  opacity: 0.6;
  pointer-events: none;
}
