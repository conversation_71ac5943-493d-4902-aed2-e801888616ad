/**
 * LPR利率管理API工具类
 * 提供LPR利率的获取、更新、历史记录等功能
 */

const API_BASE_URL = 'https://your-api-domain.com/api';

class LprApiManager {
  constructor() {
    this.cache = {
      data: null,
      timestamp: 0,
      expireTime: 60 * 60 * 1000 // 1小时缓存
    };
  }

  /**
   * 获取最新LPR利率数据
   * @returns {Promise} 返回LPR数据
   */
  async getLatestLpr() {
    try {
      // 检查缓存
      if (this.isCacheValid()) {
        console.log('使用缓存的LPR数据');
        return Promise.resolve(this.cache.data);
      }

      // 实际API调用
      const response = await this.makeRequest('/lpr/latest', 'GET');
      
      // 更新缓存
      this.updateCache(response.data);
      
      return response.data;
    } catch (error) {
      console.error('获取LPR数据失败:', error);
      // 返回默认数据
      return this.getDefaultLprData();
    }
  }

  /**
   * 获取LPR历史记录
   * @param {number} limit 记录数量限制
   * @returns {Promise} 返回历史记录
   */
  async getLprHistory(limit = 12) {
    try {
      const response = await this.makeRequest(`/lpr/history?limit=${limit}`, 'GET');
      return response.data;
    } catch (error) {
      console.error('获取LPR历史记录失败:', error);
      return this.getDefaultHistoryData();
    }
  }

  /**
   * 管理员更新LPR利率（需要管理员权限）
   * @param {Object} lprData LPR数据
   * @returns {Promise} 更新结果
   */
  async updateLpr(lprData) {
    try {
      const response = await this.makeRequest('/lpr/update', 'POST', lprData);
      
      // 清除缓存
      this.clearCache();
      
      return response;
    } catch (error) {
      console.error('更新LPR数据失败:', error);
      throw error;
    }
  }

  /**
   * 发起网络请求
   * @param {string} endpoint API端点
   * @param {string} method 请求方法
   * @param {Object} data 请求数据
   * @returns {Promise} 请求结果
   */
  makeRequest(endpoint, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
      const requestConfig = {
        url: `${API_BASE_URL}${endpoint}`,
        method: method,
        header: {
          'Content-Type': 'application/json',
          'Authorization': this.getAuthToken()
        },
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res);
          } else {
            reject(new Error(`API请求失败: ${res.statusCode}`));
          }
        },
        fail: (err) => {
          reject(err);
        }
      };

      if (data && (method === 'POST' || method === 'PUT')) {
        requestConfig.data = data;
      }

      wx.request(requestConfig);
    });
  }

  /**
   * 获取认证令牌
   * @returns {string} 认证令牌
   */
  getAuthToken() {
    // 从本地存储获取token
    try {
      return wx.getStorageSync('auth_token') || '';
    } catch (error) {
      console.error('获取认证令牌失败:', error);
      return '';
    }
  }

  /**
   * 检查缓存是否有效
   * @returns {boolean} 缓存是否有效
   */
  isCacheValid() {
    const now = Date.now();
    return this.cache.data && (now - this.cache.timestamp) < this.cache.expireTime;
  }

  /**
   * 更新缓存
   * @param {Object} data 要缓存的数据
   */
  updateCache(data) {
    this.cache.data = data;
    this.cache.timestamp = Date.now();
    
    // 同时保存到本地存储
    try {
      wx.setStorageSync('lpr_cache', {
        data: data,
        timestamp: this.cache.timestamp
      });
    } catch (error) {
      console.error('保存LPR缓存失败:', error);
    }
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.cache.data = null;
    this.cache.timestamp = 0;
    
    try {
      wx.removeStorageSync('lpr_cache');
    } catch (error) {
      console.error('清除LPR缓存失败:', error);
    }
  }

  /**
   * 从本地存储恢复缓存
   */
  restoreCache() {
    try {
      const cachedData = wx.getStorageSync('lpr_cache');
      if (cachedData && cachedData.data) {
        const now = Date.now();
        if ((now - cachedData.timestamp) < this.cache.expireTime) {
          this.cache.data = cachedData.data;
          this.cache.timestamp = cachedData.timestamp;
          console.log('从本地存储恢复LPR缓存');
        }
      }
    } catch (error) {
      console.error('恢复LPR缓存失败:', error);
    }
  }

  /**
   * 获取默认LPR数据
   * @returns {Object} 默认LPR数据
   */
  getDefaultLprData() {
    return {
      lpr1Year: 3.1,
      lpr5Year: 3.6,
      updateTime: this.getCurrentDate(),
      source: 'default'
    };
  }

  /**
   * 获取默认历史数据
   * @returns {Array} 默认历史记录
   */
  getDefaultHistoryData() {
    const currentDate = new Date();
    const history = [];
    
    for (let i = 0; i < 12; i++) {
      const date = new Date(currentDate);
      date.setMonth(date.getMonth() - i);
      
      history.push({
        date: this.formatDate(date),
        lpr1Year: 3.1 + (Math.random() - 0.5) * 0.2,
        lpr5Year: 3.6 + (Math.random() - 0.5) * 0.2
      });
    }
    
    return history;
  }

  /**
   * 获取当前日期字符串
   * @returns {string} 格式化的日期字符串
   */
  getCurrentDate() {
    return this.formatDate(new Date());
  }

  /**
   * 格式化日期
   * @param {Date} date 日期对象
   * @returns {string} 格式化的日期字符串
   */
  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  /**
   * 验证LPR数据格式
   * @param {Object} lprData LPR数据
   * @returns {boolean} 数据是否有效
   */
  validateLprData(lprData) {
    if (!lprData || typeof lprData !== 'object') {
      return false;
    }

    const { lpr1Year, lpr5Year } = lprData;
    
    return (
      typeof lpr1Year === 'number' && 
      typeof lpr5Year === 'number' &&
      lpr1Year > 0 && lpr1Year < 20 &&
      lpr5Year > 0 && lpr5Year < 20
    );
  }
}

// 创建单例实例
const lprApiManager = new LprApiManager();

// 页面加载时恢复缓存
lprApiManager.restoreCache();

module.exports = lprApiManager;
