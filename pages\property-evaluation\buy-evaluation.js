// pages/property-evaluation/buy-evaluation.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 表单数据
    formData: {
      communityName: '',
      propertyType: '',
      otherPropertyType: '',
      houseAge: '',
      buildingArea: '',
      houseType: '',
      orientation: '',
      floor: '',
      decoration: '',
      view: '',
      currentStatus: '',
      totalPrice: '',
      taxes: '',
      agencyFee: '',
      additionalInfo: ''
    },

    // 选择器索引
    pickerIndexes: {
      propertyType: -1,
      houseType: -1,
      decoration: -1,
      currentStatus: -1
    },

    // 选择器选项
    propertyTypeOptions: ['住宅', '公寓', '农民房', '其他'],
    houseTypeOptions: ['一房', '两房', '三房一卫', '三房两卫', '四房', '四房以上'],
    decorationOptions: ['毛坯', '简装', '精装'],
    currentStatusOptions: ['建设中', '现房', '业主自住', '业主出租', '空置']
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('买房评测页面加载');
    this.loadSavedData();
  },

  /**
   * 加载本地保存的数据
   */
  loadSavedData: function() {
    try {
      const savedData = wx.getStorageSync('buyEvaluationData');
      if (savedData && savedData.formData) {
        // 恢复选择器索引
        const pickerIndexes = { ...this.data.pickerIndexes };
        
        if (savedData.formData.propertyType) {
          pickerIndexes.propertyType = this.data.propertyTypeOptions.indexOf(savedData.formData.propertyType);
        }
        if (savedData.formData.houseType) {
          pickerIndexes.houseType = this.data.houseTypeOptions.indexOf(savedData.formData.houseType);
        }
        if (savedData.formData.decoration) {
          pickerIndexes.decoration = this.data.decorationOptions.indexOf(savedData.formData.decoration);
        }
        if (savedData.formData.currentStatus) {
          pickerIndexes.currentStatus = this.data.currentStatusOptions.indexOf(savedData.formData.currentStatus);
        }

        this.setData({
          formData: savedData.formData,
          pickerIndexes: pickerIndexes
        });
        
        console.log('加载保存的买房评测数据:', savedData);
      }
    } catch (e) {
      console.error('加载保存数据失败:', e);
    }
  },

  /**
   * 保存数据到本地
   */
  saveDataToLocal: function() {
    try {
      wx.setStorageSync('buyEvaluationData', {
        formData: this.data.formData,
        timestamp: Date.now()
      });
    } catch (e) {
      console.error('保存数据失败:', e);
    }
  },

  /**
   * 输入事件处理
   */
  onInput: function(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    this.setData({
      [`formData.${field}`]: value
    });
    
    // 自动保存
    this.saveDataToLocal();
  },

  /**
   * 选择器变化事件
   */
  onPickerChange: function(e) {
    const field = e.currentTarget.dataset.field;
    const index = parseInt(e.detail.value);
    let value = '';
    
    switch (field) {
      case 'propertyType':
        value = this.data.propertyTypeOptions[index];
        break;
      case 'houseType':
        value = this.data.houseTypeOptions[index];
        break;
      case 'decoration':
        value = this.data.decorationOptions[index];
        break;
      case 'currentStatus':
        value = this.data.currentStatusOptions[index];
        break;
    }

    this.setData({
      [`formData.${field}`]: value,
      [`pickerIndexes.${field}`]: index
    });

    // 如果选择了"其他"房产类型，清空之前的具体说明
    if (field === 'propertyType' && value !== '其他') {
      this.setData({
        'formData.otherPropertyType': ''
      });
    }
    
    // 自动保存
    this.saveDataToLocal();
  },

  /**
   * 单选按钮选择事件
   */
  onRadioSelect: function(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.currentTarget.dataset.value;
    
    this.setData({
      [`formData.${field}`]: value
    });
    
    // 自动保存
    this.saveDataToLocal();
  },

  /**
   * 表单验证
   */
  validateForm: function() {
    const { formData } = this.data;
    
    // 必填字段验证
    const requiredFields = [
      { field: 'communityName', name: '小区名称' },
      { field: 'propertyType', name: '房产类型' },
      { field: 'houseAge', name: '新房or二手房' },
      { field: 'buildingArea', name: '建筑面积' },
      { field: 'houseType', name: '户型' },
      { field: 'orientation', name: '朝向' },
      { field: 'floor', name: '楼层' },
      { field: 'decoration', name: '装修情况' },
      { field: 'view', name: '景观/视野' },
      { field: 'currentStatus', name: '目前状态' },
      { field: 'totalPrice', name: '房子总价' },
      { field: 'taxes', name: '税费' },
      { field: 'agencyFee', name: '中介费' }
    ];

    for (let item of requiredFields) {
      if (!formData[item.field] || formData[item.field].trim() === '') {
        wx.showToast({
          title: `请填写${item.name}`,
          icon: 'none'
        });
        return false;
      }
    }

    // 如果选择了"其他"房产类型，验证具体说明
    if (formData.propertyType === '其他' && (!formData.otherPropertyType || formData.otherPropertyType.trim() === '')) {
      wx.showToast({
        title: '请具体说明房产类型',
        icon: 'none'
      });
      return false;
    }

    // 数字字段验证
    const numericFields = [
      { field: 'buildingArea', name: '建筑面积' },
      { field: 'totalPrice', name: '房子总价' },
      { field: 'taxes', name: '税费' },
      { field: 'agencyFee', name: '中介费' }
    ];

    for (let item of numericFields) {
      const value = parseFloat(formData[item.field]);
      if (isNaN(value) || value < 0) {
        wx.showToast({
          title: `请输入有效的${item.name}`,
          icon: 'none'
        });
        return false;
      }
    }

    return true;
  },

  /**
   * 提交表单
   */
  submitForm: function() {
    if (!this.validateForm()) {
      return;
    }

    wx.showLoading({
      title: '提交中...'
    });

    // 模拟提交过程
    setTimeout(() => {
      wx.hideLoading();
      
      // 跳转到成功页面
      wx.navigateTo({
        url: '/pages/property-evaluation/success?type=buy',
        success: () => {
          // 清除保存的数据
          wx.removeStorageSync('buyEvaluationData');
        },
        fail: (err) => {
          console.error('跳转失败:', err);
          wx.showToast({
            title: '提交成功',
            icon: 'success'
          });
        }
      });
    }, 1500);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    // 页面卸载时保存数据
    this.saveDataToLocal();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: '房子应不应该买 - 买房前专业评测分析',
      desc: '专业评估，精准分析',
      path: '/pages/property-evaluation/buy-evaluation'
    };
  }
});
