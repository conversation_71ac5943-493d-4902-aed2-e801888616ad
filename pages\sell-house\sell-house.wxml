<!--pages/sell-house/sell-house.wxml-->
<view class="container">
  <!-- 表单区域 -->
  <view class="form-container">
    <!-- 房屋信息表单 -->
    <view class="form-section">
      
      <picker range="{{houseTypes}}" value="{{houseTypeIndex}}" bindchange="onHouseTypeChange" >
        <view class="form-item">
          <view class="form-label">房屋类型</view>
          <view class="form-value-container">
            <text class="form-value">{{houseTypes[houseTypeIndex]}}</text>
            <text class="arrow-text">></text>
          </view>
        </view>
      </picker>
      <!-- 小区名称 -->
      <view class="form-item">
        <view class="form-label">小区名称<text class="required-mark">*</text></view>
        <view class="form-input-container">
          <input class="form-input" placeholder="请输入小区名称" value="{{communityName}}" bindinput="onCommunityInput"/>
        </view>
      </view>

      <!-- 住房面积 -->
      <view class="form-item">
        <view class="form-label">楼栋及房号<text class="required-mark">*</text></view>
        <view class="form-input-container">
          <input class="form-input" placeholder="请输入楼栋及房号" value="{{houseArea}}" bindinput="onAreaInput"/>
          <text class="form-unit"></text>
        </view>
      </view>

      <!-- 期望售价 -->
      <view class="form-item">
        <view class="form-label">期望售价(万)</view>
        <view class="form-input-container">
          <input class="form-input" placeholder="请输入期望售价" value="{{expectedPrice}}" bindinput="onPriceInput" type="digit"/>
          <text class="form-unit"></text>
        </view>
      </view>
      <!-- 联系人 -->
      <view class="form-item">
        <view class="form-label">联系人<text class="required-mark">*</text></view>
        <view class="form-input-container">
          <input class="form-input" placeholder="请输入联系人" value="{{contactName}}" bindinput="onContactNameInput"/>
        </view>
      </view>

      <!-- 性别选择 -->
      <picker range="{{genderOptions}}" value="{{genderIndex}}" bindchange="onGenderChange" style="border-bottom: 1rpx solid rgb(204, 199, 199);">
        <view class="form-item">
          <view class="form-label">先生/女士</view>
          <view class="form-value-container">
            <text class="form-value">{{genderOptions[genderIndex]}}</text>
            <text class="arrow-text">></text>
          </view>
        </view>
      </picker>

      <!-- 联系方式 -->
      <view class="form-item" style="border-bottom: 1rpx solid rgb(204, 199, 199);">
        <view class="form-label">联系方式<text class="required-mark">*</text></view>
        <view class="form-input-container">
          <input class="form-input" placeholder="请输入联系方式" value="{{contactPhone}}" bindinput="onContactPhoneInput" type="number"/>
        </view>
      </view>
    </view>

    <!-- 备注信息 -->
    <view class="form-section">
      <view class="section-title">备注</view>
      <view class="form-item textarea-item">
        <textarea class="form-textarea" placeholder="请输入" value="{{remarks}}" bindinput="onRemarksInput" maxlength="200" style="height: 135rpx; display: block; box-sizing: border-box; left: 0rpx; top: 0rpx"></textarea>
      </view>
    </view>

    <!-- 提交记录链接 -->
    <view class="submit-record">
      <text class="record-link" bindtap="viewSubmitRecord">提交记录>></text>
    </view>
  </view>

  <!-- 用户协议勾选 -->
  <view class="agreement-container">
    <view class="agreement-checkbox-container">
      <checkbox-group bindchange="onAgreementChange">
        <checkbox class="agreement-checkbox" value="agreement" checked="{{agreementChecked}}" />
      </checkbox-group>
      <text class="agreement-text">我已阅读并同意《荣杏深房隐私政策》提交后将有专业的经纪人和您联系,核实房源并建立服务关系</text>
    </view>

    <!-- 调试信息显示区域 -->
    <view class="debug-info" wx:if="{{true}}">
      <text class="debug-title">表单验证状态：</text>
      <view class="debug-item">
        <text class="debug-label">协议勾选：</text>
        <text class="debug-value {{debugInfo.agreementChecked ? 'valid' : 'invalid'}}">{{debugInfo.agreementChecked ? '✓' : '✗'}}</text>
      </view>
      <view class="debug-item">
        <text class="debug-label">小区名称：</text>
        <text class="debug-value {{debugInfo.communityNameValid ? 'valid' : 'invalid'}}">{{debugInfo.communityNameValid ? '✓' : '✗'}}</text>
      </view>
      <view class="debug-item">
        <text class="debug-label">楼栋房号：</text>
        <text class="debug-value {{debugInfo.houseAreaValid ? 'valid' : 'invalid'}}">{{debugInfo.houseAreaValid ? '✓' : '✗'}}</text>
      </view>
      <view class="debug-item">
        <text class="debug-label">联系人：</text>
        <text class="debug-value {{debugInfo.contactNameValid ? 'valid' : 'invalid'}}">{{debugInfo.contactNameValid ? '✓' : '✗'}}</text>
      </view>
      <view class="debug-item">
        <text class="debug-label">手机号：</text>
        <text class="debug-value {{debugInfo.phoneValid ? 'valid' : 'invalid'}}">{{debugInfo.phoneValid ? '✓' : '✗'}}</text>
      </view>
      <view class="debug-item">
        <text class="debug-label">可提交：</text>
        <text class="debug-value {{canSubmit ? 'valid' : 'invalid'}}">{{canSubmit ? '✓' : '✗'}}</text>
      </view>

      <!-- 测试按钮 -->
      <view class="debug-buttons">
        <button class="debug-btn" bindtap="fillTestData" size="mini">填充测试数据</button>
        <button class="debug-btn" bindtap="clearAllData" size="mini">清空所有数据</button>
      </view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-container">
    <button class="submit-btn {{canSubmit ? '' : 'disabled'}}"
            bindtap="onSubmit"
            disabled="{{!canSubmit}}"
            style="width: 650rpx; {{canSubmit ? 'background: linear-gradient(135deg, #FF4444 0%, #FF6B6B 100%); color: white; box-shadow: 0 8rpx 24rpx rgba(255, 68, 68, 0.3);' : 'background: #ccc; color: #999; box-shadow: none;'}}">
      确认提交
    </button>
  </view>
</view>
