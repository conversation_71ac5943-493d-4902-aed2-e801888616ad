/* pages/loan-consultation/loan-consultation.wxss */

.container {
  background: #f5f5f5;
  padding-bottom: 120rpx;
  width: 100%;
}
.lop{
 width: 100%;
 margin-top:-210rpx ;
}

/* 顶部标题区域 */
.header-section {
  position: relative;
  height: 400rpx;
  overflow: hidden;
}

.header-bg {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #4169E1 0%, #1E90FF 100%);
  position: relative;
}

/* 网格背景图案 */
.header-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px);
  background-size: 40rpx 40rpx;
  opacity: 0.3;
}

.header-content {
  position: relative;
  z-index: 2;
  padding: 60rpx 40rpx;
  text-align: center;
  color: white;
}

.main-title {
  font-size: 56rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  letter-spacing: 4rpx;
}

.sub-title {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 30rpx;
}

.tag-container {
  display: flex;
  justify-content: center;
  gap: 20rpx;
}

.tag {
  background: rgba(255, 255, 255, 0.2);
  padding: 10rpx 20rpx;
  border-radius: 25rpx;
  font-size: 26rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
}

/* 咨询卡片 */
.consultation-card {
  margin: 30rpx;
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.card-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.card-underline {
  width: 80rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #4A90E2, #357ABD);
  margin: 15rpx auto 0;
  border-radius: 3rpx;
}

.consultation-item {
  margin-bottom: 30rpx;
}

.item-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.item-icon {
  width: 40rpx;
  height: 40rpx;
  background: #4A90E2;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 15rpx;
}

.item-title {
  flex: 1;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.chart-icon {
  font-size: 32rpx;
}

.item-content {
  padding-left: 55rpx;
}

.content-point {
  display: flex;
  margin-bottom: 15rpx;
  align-items: flex-start;
}

.point-dot {
  color: #4A90E2;
  font-size: 24rpx;
  margin-right: 10rpx;
  margin-top: 5rpx;
}

.point-text {
  flex: 1;
  font-size: 28rpx;
  line-height: 1.6;
  color: #666;
}

/* 价格区域 */
.price-section {
  margin: 0 30rpx 30rpx;
  background: white;
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  width: 100%;
  position: relative;
  margin-bottom: 1rpx;
}

.price-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.service-type {
  font-size: 28rpx;
  color: #666;
  position: absolute;
  top: 20rpx;
  left: 55rpx;
}

.price-container {
  display: flex;
  align-items: center;
  position: relative;
  top: 30rpx;
  left: 35rpx;
}

.price-symbol {
  font-size: 32rpx;
  color: #FF4444;
  font-weight: bold;
}

.price-amount {
  font-size: 48rpx;
  color: #FF4444;
  font-weight: bold;
}

.buy-count {
  font-size: 24rpx;
  color: #999;
  position: absolute;
  top: 80rpx;
  right: 60rpx;
}

/* 销售导语 */
.sales-description {
  margin:30rpx 20rpx;
  background: white;
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  width: 100%;
  margin: 10rpx;
}

.description-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  margin-left: 20rpx;
}

.description-content {
  line-height: 1.8;
}

.description-text {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-left: 20rpx;
}
.wq{
  font-size: 28rpx;
}
.we{
  font-size: 28rpx;
}
.wp{
  font-size: 28rpx;
  color: purple;
}

/* 咨询流程 */
.consultation-process {
  margin: 0 30rpx 30rpx;
  background: white;
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  width: 750rpx;
}

.process-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
  margin-left: 20rpx;
}

.process-item {
  font-size: 28rpx;
  color: #666;
  display: block;
  line-height: 2.0;
  padding: 0 20rpx;
}

.process-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.process-step {
  font-size: 28rpx;
  font-weight: bold;
  color: #4A90E2;
  margin-bottom: 10rpx;
}

.process-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 购买区域 */
.purchase-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0);
  padding: 20rpx 30rpx 40rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.purchase-card {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.purchase-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 10rpx;
  overflow: hidden;
  margin-right: 20rpx;
}

.purchase-image image {
  width: 100%;
  height: 100%;
}

.purchase-info {
  flex: 1;
}

.purchase-price {
  display: flex;
  align-items: baseline;
  margin-bottom: 10rpx;
}

.purchase-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.detail-label {
  font-size: 24rpx;
  color: #666;
}

.detail-tag {
  background: #f0f0f0;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: #333;
  border: 1rpx solid #e0e0e0;
}

.purchase-button {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #FF4444 0%, #FF6B6B 100%);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(255, 68, 68, 0.3);
}

/* 弹窗通用样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 1000;
}

/* 拖拽指示器 */
.modal-handle {
  width: 80rpx;
  height: 8rpx;
  background: #e0e0e0;
  border-radius: 4rpx;
  margin: 20rpx auto 10rpx;
}

.modal-close {
  position: absolute;
  top: 30rpx;
  right: 20rpx;
  width: 50rpx;
  height: 50rpx;
  background: rgba(0, 0, 0, 0.1);
  color: #999;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  z-index: 1001;
}

/* 第一步：购买弹窗样式 */
.purchase-modal {
  width: 100%;
  background: white;
  border-radius: 30rpx 30rpx 0 0;
  position: relative;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.15);
  animation: slideUp 0.3s ease-out;
  height: 50%;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.modal-content {
  display: flex;
  padding: 30rpx;
  gap: 30rpx;
}

/* 左侧图片区域 */
.modal-left {
  width: 160rpx;
  height: 200rpx;
  flex-shrink: 0;
}

.modal-left image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  object-fit: cover;
}

/* 右侧内容区域 */
.modal-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 价格显示 */
.modal-price {
  display: flex;
  align-items: baseline;
  margin-bottom: 10rpx;
  position: relative;
  top: 40rpx;
}

.price-symbol {
  font-size: 32rpx;
  color: #FF4444;
  font-weight: bold;
  margin-right: 4rpx;
}

.price-amount {
  font-size: 48rpx;
  color: #FF4444;
  font-weight: bold;
}
.lllo{
  position: relative;
  top: 120rpx;
  left: -180rpx;
}

/* 信息区块 */
.modal-section {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.section-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.section-tag {
  display: inline-block;
  background: #FFF5E6;
  border: 1rpx solid #FFB366;
  color: #FF8533;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  align-self: flex-start;
}

/* 购买按钮 */
.modal-purchase-btn {
  width: calc(100% - 60rpx);
  height: 88rpx;
  background: linear-gradient(135deg, #FF4444 0%, #FF6B6B 100%);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 30rpx 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 68, 68, 0.3);
  position: relative;
  top: 120rpx;
}

/* 第二步：支付确认弹窗样式 */
.payment-modal {
  width: 100%;
  background: white;
  border-radius: 30rpx 30rpx 0 0;
  position: relative;
  animation: slideUp 0.3s ease-out;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.15);
}

.payment-content {
  padding: 50rpx 40rpx;
  text-align: center;
}

.payment-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.payment-amount {
  margin-bottom: 10rpx;
}

.payment-amount text:first-child {
  font-size: 28rpx;
  color: #666;
}

.payment-amount .amount {
  font-size: 40rpx;
  color: #FF4444;
  font-weight: bold;
}

.payment-service {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.payment-buttons {
  display: flex;
  gap: 20rpx;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: bold;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
  border: 1rpx solid #e0e0e0;
}

.confirm-btn {
  background: linear-gradient(135deg, #FF4444 0%, #FF6B6B 100%);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(255, 68, 68, 0.3);
}

/* 第三步：二维码弹窗样式 */
.qrcode-modal {
  width: 100%;
  max-height: 80vh;
  background: white;
  border-radius: 30rpx 30rpx 0 0;
  position: relative;
  animation: slideUp 0.3s ease-out;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.15);
}

.qrcode-content {
  padding: 50rpx 40rpx;
  text-align: center;
}

.qrcode-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.qrcode-subtitle {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.qrcode-image {
  width: 300rpx;
  height: 300rpx;
  margin: 0 auto 30rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 15rpx;
  overflow: hidden;
  background: #f9f9f9;
}

.qrcode-image image {
  width: 100%;
  height: 100%;
}

.qrcode-tips {
  margin-bottom: 40rpx;
}

.qrcode-tips text {
  display: block;
  font-size: 24rpx;
  color: #999;
  line-height: 1.6;
  margin-bottom: 8rpx;
}

.qrcode-close-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #FF4444 0%, #FF6B6B 100%);
  color: white;
  font-size: 30rpx;
  font-weight: bold;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(65, 105, 225, 0.3);
}
